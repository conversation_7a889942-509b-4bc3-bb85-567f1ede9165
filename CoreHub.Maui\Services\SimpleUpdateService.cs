using System.Text.Json;
using CoreHub.Shared.Models.AppUpdate;
using CoreHub.Shared.Services;

#if ANDROID
using Android.OS;
using AndroidSettings = Android.Provider.Settings;
using JavaLocale = Java.Util.Locale;
using JavaTimeZone = Java.Util.TimeZone;
#endif

namespace CoreHub.Maui.Services
{
    /// <summary>
    /// 简化的更新服务实现，参考MauiScanManager设计
    /// </summary>
    public class SimpleUpdateService : ISimpleUpdateService
    {
        private readonly HttpClient _httpClient;
        private readonly IApplicationLogger _logger;
        private readonly IServiceProvider _serviceProvider;
        private readonly string _baseUrl;
        private UpdateCheckResponse? _latestUpdateInfo;
        private static bool _isUpdating = false;
        private static bool _hasChecked = false;

        public event EventHandler<UpdateProgressEventArgs>? UpdateProgress;

        public SimpleUpdateService(HttpClient httpClient, IApplicationLogger logger, IServiceProvider serviceProvider)
        {
            _httpClient = httpClient;
            _logger = logger;
            _serviceProvider = serviceProvider;
            _baseUrl = "https://************:8081"; // 硬编码基础URL

            // 设置HttpClient的BaseAddress
            if (_httpClient.BaseAddress == null)
            {
                _httpClient.BaseAddress = new Uri(_baseUrl);
            }
        }

        public bool IsUpdating => _isUpdating;
        public bool HasChecked => _hasChecked;
        public bool IsForceUpdate => _latestUpdateInfo?.IsForceUpdate ?? false;

        public async Task<bool> CheckForUpdate()
        {
            try
            {
                _isUpdating = true;
                var platform = Microsoft.Maui.Devices.DeviceInfo.Platform == DevicePlatform.Android ? "android" : "ios";
                
                _logger.LogInformation("正在检查更新，URL: {BaseUrl}/api/AppUpdate/check", _baseUrl);
                _logger.LogInformation("网络状态: {NetworkAccess}", Connectivity.NetworkAccess);

                if (Connectivity.NetworkAccess != NetworkAccess.Internet)
                {
                    _logger.LogWarning("无网络连接");
                    return false;
                }

                var (currentVersion, currentVersionCode) = await GetCurrentVersionAsync();
                var deviceInfo = await GetDeviceInfoAsync();

                var request = new UpdateCheckRequest
                {
                    CurrentVersion = currentVersion,
                    CurrentVersionCode = currentVersionCode,
                    Platform = "Android",
                    DeviceId = GetDeviceId(),
                    Device = deviceInfo
                };

                var json = JsonSerializer.Serialize(request);
                var content = new StringContent(json, System.Text.Encoding.UTF8, "application/json");

                using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(30));
                var response = await _httpClient.PostAsync("/api/AppUpdate/check", content, cts.Token);
                
                _logger.LogInformation("服务器响应状态码: {StatusCode}", response.StatusCode);
                if (!response.IsSuccessStatusCode) 
                {
                    _logger.LogWarning("服务器返回错误: {StatusCode}", response.StatusCode);
                    return false;
                }

                var jsonString = await response.Content.ReadAsStringAsync();
                _logger.LogInformation("服务器响应内容: {JsonString}", jsonString);

                var options = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                };
                
                _latestUpdateInfo = JsonSerializer.Deserialize<UpdateCheckResponse>(jsonString, options);
                _logger.LogInformation("反序列化后的版本号: {Version}", _latestUpdateInfo?.LatestVersion?.VersionNumber ?? "null");
                
                var currentVersionString = AppInfo.Current.VersionString;
                _logger.LogInformation("当前版本: {CurrentVersion}, 最新版本: {LatestVersion}", 
                    currentVersionString, _latestUpdateInfo?.LatestVersion?.VersionNumber ?? "null");

                if (_latestUpdateInfo?.HasUpdate != true)
                {
                    _logger.LogInformation("没有可用更新");
                    _isUpdating = false;
                }
                
                _hasChecked = true;
                return _latestUpdateInfo?.HasUpdate ?? false;
            }
            catch (Exception ex)
            {
                _isUpdating = false;
                _logger.LogError(ex, "检查更新时出错: {Message}", ex.Message);
                _hasChecked = true;
                throw;
            }
        }

        public async Task<bool> DownloadAndInstallUpdate()
        {
            if (_latestUpdateInfo?.LatestVersion == null)
            {
                _logger.LogError(new Exception("更新信息为空"), "更新信息为空");
                return false;
            }

#if ANDROID
            var androidUpdateService = _serviceProvider.GetService<IClientUpdateService>();
            if (androidUpdateService != null)
            {
                var progress = new Progress<DownloadProgress>(OnDownloadProgress);
                var result = await androidUpdateService.DownloadUpdateAsync(
                    _latestUpdateInfo.LatestVersion,
                    progress);

                if (result.IsSuccess && !string.IsNullOrEmpty(result.FilePath))
                {
                    var installResult = await androidUpdateService.InstallUpdateAsync(result.FilePath);
                    return installResult.IsSuccess;
                }

                return false;
            }
#endif
            return false;
        }

        private void OnDownloadProgress(DownloadProgress downloadProgress)
        {
            var progress = downloadProgress.ProgressPercentage / 100.0;
            var status = $"正在下载: {downloadProgress.ProgressPercentage}% ({downloadProgress.BytesReceived / 1024.0:F2}/{downloadProgress.TotalBytesToReceive / 1024.0:F2} KB)";
            
            UpdateProgress?.Invoke(this, new UpdateProgressEventArgs
            {
                Progress = progress,
                Status = status
            });
        }

        private async Task<(string version, int versionCode)> GetCurrentVersionAsync()
        {
            return await Task.Run(() =>
            {
                var version = AppInfo.Current.VersionString;
                var versionCode = 1; // 默认版本代码
                
#if ANDROID
                try
                {
                    var context = Platform.CurrentActivity ?? global::Android.App.Application.Context;
                    var packageInfo = context.PackageManager?.GetPackageInfo(context.PackageName, 0);
                    if (packageInfo != null)
                    {
                        versionCode = (int)packageInfo.LongVersionCode;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "获取版本代码时发生异常");
                }
#endif
                
                return (version, versionCode);
            });
        }

        private async Task<CoreHub.Shared.Models.AppUpdate.DeviceInfo> GetDeviceInfoAsync()
        {
            return await Task.Run(() =>
            {
                try
                {
#if ANDROID
                    return new CoreHub.Shared.Models.AppUpdate.DeviceInfo
                    {
                        Model = $"{Build.Manufacturer} {Build.Model}",
                        OsVersion = Build.VERSION.Release,
                        Language = JavaLocale.Default?.Language ?? "zh",
                        TimeZone = JavaTimeZone.Default?.ID ?? "Asia/Shanghai"
                    };
#else
                    return new CoreHub.Shared.Models.AppUpdate.DeviceInfo();
#endif
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "获取设备信息时发生异常");
                    return new CoreHub.Shared.Models.AppUpdate.DeviceInfo();
                }
            });
        }

        private string GetDeviceId()
        {
            try
            {
#if ANDROID
                var context = Platform.CurrentActivity ?? Android.App.Application.Context;
                var androidId = AndroidSettings.Secure.GetString(
                    context.ContentResolver,
                    AndroidSettings.Secure.AndroidId);
                return androidId ?? "unknown";
#else
                return "unknown";
#endif
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取设备ID时发生异常");
                return "unknown";
            }
        }
    }
}
