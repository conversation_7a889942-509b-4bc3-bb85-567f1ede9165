<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="CoreHub.Maui.Pages.SimpleUpdatePage"
             Title="应用更新"
             BackgroundColor="#F5F5F5">
    <VerticalStackLayout Spacing="20" Padding="20">
        <Label Text="正在下载更新..."
               HorizontalOptions="Center" 
               FontSize="18"
               FontAttributes="Bold"/>
        <ProgressBar x:Name="UpdateProgress"
                    Progress="0"
                    HorizontalOptions="Fill" 
                    HeightRequest="10"/>
        <Label x:Name="StatusLabel"
               HorizontalOptions="Center"
               FontSize="14"
               TextColor="#666666"/>
        <Button Text="查看日志"
                Clicked="OnViewLogsClicked"
                HorizontalOptions="Center"
                BackgroundColor="#007ACC"
                TextColor="White"
                CornerRadius="5"
                Padding="20,10"/>
    </VerticalStackLayout>
</ContentPage>
