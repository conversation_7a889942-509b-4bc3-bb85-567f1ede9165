using CoreHub.Maui.Services;
using CoreHub.Shared.Services;

namespace CoreHub.Maui.Pages
{
    /// <summary>
    /// 简化的更新页面，参考MauiScanManager设计
    /// </summary>
    public partial class SimpleUpdatePage : ContentPage
    {
        private readonly ISimpleUpdateService _updateService;
        private readonly IApplicationLogger _logger;
        
        public SimpleUpdatePage(ISimpleUpdateService updateService, IApplicationLogger logger)
        {
            InitializeComponent();
            _updateService = updateService;
            _logger = logger;
            
            _updateService.UpdateProgress += OnUpdateProgress;
            StartUpdate();
        }
        
        private async void StartUpdate()
        {
            try
            {
                var success = await _updateService.DownloadAndInstallUpdate();
                if (!success)
                {
                    await DisplayAlert("错误", "安装更新失败", "确定");
                    await Navigation.PopModalAsync();
                }
                else
                {
                    // 添加延迟，确保安装对话框有时间显示
                    await Task.Delay(2000);
                    // 如果安装对话框没有显示，提示用户
                    var retry = await DisplayAlert("提示", 
                        "如果没有看到安装提示，是否重试？", 
                        "重试", "取消");
                    
                    if (retry)
                    {
                        StartUpdate();
                    }
                    else
                    {
                        await Navigation.PopModalAsync();
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新过程出错: {Message}", ex.Message);
                await DisplayAlert("错误", "更新过程出错", "确定");
                await Navigation.PopModalAsync();
            }
        }
        
        private void OnUpdateProgress(object? sender, UpdateProgressEventArgs e)
        {
            MainThread.BeginInvokeOnMainThread(() =>
            {
                UpdateProgress.Progress = e.Progress;
                StatusLabel.Text = e.Status;
            });
        }
        
        protected override void OnDisappearing()
        {
            base.OnDisappearing();
            _updateService.UpdateProgress -= OnUpdateProgress;
        }

        private async void OnViewLogsClicked(object sender, EventArgs e)
        {
            // 这里可以显示日志，暂时显示简单信息
            await DisplayAlert("更新日志", "更新正在进行中...", "确定");
        }
    }
}
