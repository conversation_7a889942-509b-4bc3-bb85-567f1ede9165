using CoreHub.Shared.Models.AppUpdate;
using CoreHub.Shared.Services;
using System.Text;

namespace CoreHub.Maui.Pages
{
    /// <summary>
    /// 更新功能测试页面
    /// </summary>
    public partial class UpdateTestPage : ContentPage
    {
        private readonly IClientUpdateService? _updateService;
        private readonly IApplicationLogger? _logger;
        private readonly StringBuilder _logBuilder = new();
        private string? _downloadedFilePath;

        public UpdateTestPage()
        {
            InitializeComponent();
            
            // 获取服务
            var serviceProvider = Handler?.MauiContext?.Services;
            if (serviceProvider != null)
            {
                _updateService = serviceProvider.GetService<IClientUpdateService>();
                _logger = serviceProvider.GetService<IApplicationLogger>();
            }
            
            LoadCurrentVersionInfo();
        }

        protected override void OnAppearing()
        {
            base.OnAppearing();
            
            // 如果服务还没有获取到，再次尝试
            if (_updateService == null || _logger == null)
            {
                var serviceProvider = Handler?.MauiContext?.Services;
                if (serviceProvider != null)
                {
                    var updateService = serviceProvider.GetService<IClientUpdateService>();
                    var logger = serviceProvider.GetService<IApplicationLogger>();
                    
                    if (updateService != null && logger != null)
                    {
                        AddLog("服务获取成功");
                    }
                    else
                    {
                        AddLog("警告: 无法获取更新服务或日志服务");
                    }
                }
            }
        }

        private void LoadCurrentVersionInfo()
        {
            try
            {
                var version = AppInfo.Current.VersionString;
                var build = AppInfo.Current.BuildString;
                
                CurrentVersionLabel.Text = $"版本: {version}";
                CurrentVersionCodeLabel.Text = $"构建号: {build}";
                
                AddLog($"当前版本: {version}, 构建号: {build}");
            }
            catch (Exception ex)
            {
                AddLog($"获取版本信息失败: {ex.Message}");
            }
        }

        private async void OnDownloadClicked(object sender, EventArgs e)
        {
            if (_updateService == null)
            {
                AddLog("错误: 更新服务未初始化");
                return;
            }

            try
            {
                DownloadButton.IsEnabled = false;
                DownloadProgressBar.IsVisible = true;
                ProgressLabel.IsVisible = true;
                DownloadProgressBar.Progress = 0;
                
                var downloadUrl = DownloadUrlEntry.Text?.Trim();
                var versionNumber = VersionNumberEntry.Text?.Trim();
                
                if (string.IsNullOrEmpty(downloadUrl) || string.IsNullOrEmpty(versionNumber))
                {
                    AddLog("错误: 请输入下载URL和版本号");
                    return;
                }
                
                AddLog($"开始下载: {downloadUrl}");
                AddLog($"版本号: {versionNumber}");
                
                // 创建版本信息
                var versionInfo = new VersionInfo
                {
                    VersionNumber = versionNumber,
                    DownloadUrl = downloadUrl
                };
                
                // 创建进度报告器
                var progress = new Progress<DownloadProgress>(OnDownloadProgress);
                
                // 开始下载
                var result = await _updateService.DownloadUpdateAsync(versionInfo, progress);
                
                if (result.IsSuccess && !string.IsNullOrEmpty(result.FilePath))
                {
                    _downloadedFilePath = result.FilePath;
                    DownloadedFileLabel.Text = $"下载的文件: {Path.GetFileName(result.FilePath)}";
                    InstallButton.IsEnabled = true;
                    AddLog($"下载成功: {result.FilePath}");
                }
                else
                {
                    AddLog($"下载失败: {result.ErrorMessage ?? "未知错误"}");
                }
            }
            catch (Exception ex)
            {
                AddLog($"下载异常: {ex.Message}");
                _logger?.LogError(ex, "下载测试失败");
            }
            finally
            {
                DownloadButton.IsEnabled = true;
                DownloadProgressBar.IsVisible = false;
                ProgressLabel.IsVisible = false;
            }
        }

        private void OnDownloadProgress(DownloadProgress downloadProgress)
        {
            MainThread.BeginInvokeOnMainThread(() =>
            {
                var progressPercent = downloadProgress.ProgressPercentage;
                DownloadProgressBar.Progress = progressPercent / 100.0;
                
                var downloadedMB = downloadProgress.BytesReceived / 1024.0 / 1024.0;
                var totalMB = downloadProgress.TotalBytesToReceive / 1024.0 / 1024.0;
                
                ProgressLabel.Text = $"进度: {progressPercent:F1}% ({downloadedMB:F2}/{totalMB:F2} MB)";
                
                if (progressPercent % 10 == 0) // 每10%记录一次日志
                {
                    AddLog($"下载进度: {progressPercent:F1}%");
                }
            });
        }

        private async void OnInstallClicked(object sender, EventArgs e)
        {
            if (_updateService == null || string.IsNullOrEmpty(_downloadedFilePath))
            {
                AddLog("错误: 没有可安装的文件");
                return;
            }

            try
            {
                InstallButton.IsEnabled = false;
                AddLog($"开始安装: {_downloadedFilePath}");
                
                var result = await _updateService.InstallUpdateAsync(_downloadedFilePath);
                
                if (result.IsSuccess)
                {
                    AddLog("安装成功，请查看系统安装对话框");
                }
                else
                {
                    AddLog($"安装失败: {result.ErrorMessage ?? "未知错误"}");
                }
            }
            catch (Exception ex)
            {
                AddLog($"安装异常: {ex.Message}");
                _logger?.LogError(ex, "安装测试失败");
            }
            finally
            {
                InstallButton.IsEnabled = true;
            }
        }

        private void OnClearLogClicked(object sender, EventArgs e)
        {
            _logBuilder.Clear();
            LogLabel.Text = "日志已清空";
        }

        private void AddLog(string message)
        {
            var timestamp = DateTime.Now.ToString("HH:mm:ss");
            var logMessage = $"[{timestamp}] {message}";
            
            _logBuilder.AppendLine(logMessage);
            
            MainThread.BeginInvokeOnMainThread(() =>
            {
                LogLabel.Text = _logBuilder.ToString();
            });
            
            _logger?.LogInformation("UpdateTest: {Message}", message);
        }
    }
}
