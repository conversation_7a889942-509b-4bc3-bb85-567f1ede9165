using CoreHub.Shared.Services;
using System.Text;

namespace CoreHub.Maui.Pages
{
    /// <summary>
    /// 更新功能测试页面
    /// </summary>
    public partial class UpdateTestPage : ContentPage
    {
        private readonly IClientUpdateService? _updateService;
        private readonly IApplicationLogger? _logger;
        private readonly StringBuilder _logBuilder = new();

        public UpdateTestPage()
        {
            InitializeComponent();
            
            // 尝试获取服务
            try
            {
                var serviceProvider = Handler?.MauiContext?.Services;
                if (serviceProvider != null)
                {
                    _updateService = serviceProvider.GetService<IClientUpdateService>();
                    _logger = serviceProvider.GetService<IApplicationLogger>();
                }
            }
            catch (Exception ex)
            {
                AddLog($"获取服务失败: {ex.Message}");
            }

            LoadCurrentVersion();
        }

        protected override void OnAppearing()
        {
            base.OnAppearing();
            
            // 如果服务未初始化，重试获取
            if (_updateService == null || _logger == null)
            {
                try
                {
                    var serviceProvider = Handler?.MauiContext?.Services;
                    if (serviceProvider != null)
                    {
                        var updateService = serviceProvider.GetService<IClientUpdateService>();
                        var logger = serviceProvider.GetService<IApplicationLogger>();
                        
                        if (updateService != null && logger != null)
                        {
                            AddLog("服务初始化成功");
                        }
                        else
                        {
                            AddLog("服务仍未初始化");
                        }
                    }
                }
                catch (Exception ex)
                {
                    AddLog($"重试获取服务失败: {ex.Message}");
                }
            }
        }

        private void LoadCurrentVersion()
        {
            try
            {
                var version = AppInfo.Current.VersionString;
                CurrentVersionLabel.Text = $"版本: {version}";
                AddLog($"当前版本: {version}");
            }
            catch (Exception ex)
            {
                CurrentVersionLabel.Text = "版本: 获取失败";
                AddLog($"获取版本失败: {ex.Message}");
            }
        }

        private async void OnCheckUpdateClicked(object sender, EventArgs e)
        {
            if (_updateService == null)
            {
                AddLog("更新服务未初始化");
                await DisplayAlert("错误", "更新服务未初始化", "确定");
                return;
            }

            try
            {
                CheckUpdateButton.IsEnabled = false;
                CheckUpdateButton.Text = "检查中...";
                UpdateStatusLabel.Text = "更新状态: 检查中...";
                
                AddLog("开始检查更新...");

                var updateResponse = await _updateService.CheckForUpdateAsync(silent: false);

                if (updateResponse.HasUpdate)
                {
                    UpdateStatusLabel.Text = $"更新状态: 有更新 (强制: {updateResponse.IsForceUpdate})";
                    AddLog($"检查完成: 有可用更新，强制更新: {updateResponse.IsForceUpdate}");

                    if (updateResponse.IsForceUpdate)
                    {
                        await DisplayAlert("发现更新", "检测到强制更新，更新页面将自动显示", "确定");
                    }
                    else
                    {
                        var result = await DisplayAlert("发现更新", "检测到可选更新，是否立即更新？", "更新", "稍后");
                        if (result)
                        {
                            await ShowUpdatePageAsync();
                        }
                    }
                }
                else
                {
                    UpdateStatusLabel.Text = "更新状态: 已是最新版本";
                    AddLog("检查完成: 当前已是最新版本");
                    await DisplayAlert("检查更新", "当前已是最新版本", "确定");
                }
            }
            catch (Exception ex)
            {
                UpdateStatusLabel.Text = "更新状态: 检查失败";
                AddLog($"检查更新失败: {ex.Message}");
                await DisplayAlert("错误", $"检查更新失败: {ex.Message}", "确定");
            }
            finally
            {
                CheckUpdateButton.IsEnabled = true;
                CheckUpdateButton.Text = "检查更新";
            }
        }

        private async void OnForceUpdateClicked(object sender, EventArgs e)
        {
            AddLog("模拟强制更新场景");
            await DisplayAlert("提示", "这是强制更新测试，实际项目中由服务器控制", "确定");
        }

        private async void OnShowUpdatePageClicked(object sender, EventArgs e)
        {
            await ShowUpdatePageAsync();
        }

        private async Task ShowUpdatePageAsync()
        {
            try
            {
                if (_updateService == null || _logger == null)
                {
                    AddLog("服务未初始化，无法显示更新页面");
                    await DisplayAlert("错误", "服务未初始化", "确定");
                    return;
                }

                AddLog("显示更新页面");
                var updatePage = new SimpleUpdatePage(_updateService, _logger);
                await Navigation.PushModalAsync(updatePage);
            }
            catch (Exception ex)
            {
                AddLog($"显示更新页面失败: {ex.Message}");
                await DisplayAlert("错误", $"显示更新页面失败: {ex.Message}", "确定");
            }
        }

        private void OnClearLogClicked(object sender, EventArgs e)
        {
            _logBuilder.Clear();
            LogLabel.Text = "日志已清除";
        }

        private void AddLog(string message)
        {
            var timestamp = DateTime.Now.ToString("HH:mm:ss");
            _logBuilder.AppendLine($"[{timestamp}] {message}");
            
            MainThread.BeginInvokeOnMainThread(() =>
            {
                LogLabel.Text = _logBuilder.ToString();
            });
        }
    }
}
