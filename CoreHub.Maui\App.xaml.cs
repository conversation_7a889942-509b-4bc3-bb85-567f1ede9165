using CoreHub.Shared.Services;

namespace CoreHub
{
    public partial class App : Application
    {
        private IClientUpdateService? _updateService;
        private IApplicationLogger? _logger;
        private bool _updateCheckInProgress = false;

        public App()
        {
            try
            {
                InitializeComponent();

                // 设置全局异常处理
                AppDomain.CurrentDomain.UnhandledException += OnUnhandledException;
                TaskScheduler.UnobservedTaskException += OnUnobservedTaskException;

                MainPage = new AppShell();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"App Constructor Error: {ex}");
                throw;
            }
        }

        protected override async void OnStart()
        {
            base.OnStart();

            // 异步启动更新检查，避免阻塞应用启动
            _ = Task.Run(CheckForUpdatesAsync);
        }

        private async Task CheckForUpdatesAsync()
        {
            // 防止重复检查
            if (_updateCheckInProgress)
            {
                System.Diagnostics.Debug.WriteLine("更新检查已在进行中，跳过重复检查");
                return;
            }

            try
            {
                _updateCheckInProgress = true;
                // 尝试从服务提供器获取服务
                var serviceProvider = Handler?.MauiContext?.Services;
                if (serviceProvider != null)
                {
                    _updateService = serviceProvider.GetService<IClientUpdateService>();
                    _logger = serviceProvider.GetService<IApplicationLogger>();
                }

                if (_updateService == null || _logger == null)
                {
                    System.Diagnostics.Debug.WriteLine("更新服务未初始化");
                    return;
                }

                // 检查网络连接
                if (Connectivity.NetworkAccess != NetworkAccess.Internet)
                {
                    _logger.LogWarning("无网络连接，跳过更新检查");
                    return;
                }

                _logger.LogInformation("开始检查更新...");

                // 检查更新
                var updateResponse = await _updateService.CheckForUpdateAsync(silent: true);

                if (updateResponse.HasUpdate)
                {
                    if (updateResponse.IsForceUpdate)
                    {
                        _logger.LogInformation("检测到强制更新，更新页面将自动显示");
                        // 强制更新会在CheckForUpdateAsync中自动显示更新页面
                    }
                    else
                    {
                        _logger.LogInformation("检测到可选更新，显示确认对话框");
                        bool shouldUpdate = await MainPage.DisplayAlert(
                            "发现新版本",
                            "是否立即更新？",
                            "更新",
                            "稍后");

                        if (shouldUpdate)
                        {
                            await ShowUpdatePageAsync();
                        }
                    }
                }
                else
                {
                    _logger.LogInformation("当前已是最新版本");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "检查更新失败: {Message}", ex.Message);
                System.Diagnostics.Debug.WriteLine($"检查更新失败: {ex.Message}");
            }
            finally
            {
                _updateCheckInProgress = false;
            }
        }

        private async Task ShowUpdatePageAsync()
        {
            try
            {
                if (_updateService == null || _logger == null)
                {
                    return;
                }

                // 如果是AndroidUpdateService，直接调用其ShowUpdatePageAsync方法
#if ANDROID
                if (_updateService is Platforms.Android.AndroidUpdateService androidUpdateService)
                {
                    await androidUpdateService.ShowUpdatePageAsync();
                }
                else
#endif
                {
                    _logger.LogWarning("当前平台不支持显示更新页面");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "显示更新页面失败: {Message}", ex.Message);
                System.Diagnostics.Debug.WriteLine($"显示更新页面失败: {ex.Message}");
            }
        }

        private void OnUnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            var exception = e.ExceptionObject as Exception;
            System.Diagnostics.Debug.WriteLine($"Unhandled Exception: {exception}");

            // 在这里可以添加错误报告或日志记录
        }

        private void OnUnobservedTaskException(object? sender, UnobservedTaskExceptionEventArgs e)
        {
            System.Diagnostics.Debug.WriteLine($"Unobserved Task Exception: {e.Exception}");
            e.SetObserved(); // 防止应用崩溃
        }
    }
}
