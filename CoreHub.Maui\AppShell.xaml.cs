using CoreHub.Shared.Services;

namespace CoreHub
{
    public partial class AppShell : Shell
    {
        public AppShell()
        {
            InitializeComponent();
        }

        protected override void OnAppearing()
        {
            base.OnAppearing();

            // 在Shell完全加载后启动更新检查
            _ = Task.Run(async () =>
            {
                await Task.Delay(3000); // 等待Shell完全加载
                await CheckForUpdatesAsync();
            });
        }

        private async Task CheckForUpdatesAsync()
        {
            try
            {
                // 获取服务
                var serviceProvider = Handler?.MauiContext?.Services;
                if (serviceProvider == null)
                {
                    System.Diagnostics.Debug.WriteLine("无法获取服务提供器");
                    return;
                }

                var updateService = serviceProvider.GetService<IClientUpdateService>();
                var logger = serviceProvider.GetService<IApplicationLogger>();

                if (updateService == null || logger == null)
                {
                    System.Diagnostics.Debug.WriteLine("更新服务未初始化");
                    return;
                }

                // 检查网络连接
                if (Connectivity.NetworkAccess != NetworkAccess.Internet)
                {
                    logger.LogWarning("无网络连接，跳过更新检查");
                    return;
                }

                logger.LogInformation("Shell: 开始检查更新...");

                // 检查更新
                var updateResponse = await updateService.CheckForUpdateAsync(silent: true);

                if (updateResponse.HasUpdate)
                {
                    MainThread.BeginInvokeOnMainThread(async () =>
                    {
                        if (updateResponse.IsForceUpdate)
                        {
                            logger.LogInformation("检测到强制更新，更新页面将自动显示");
                            // 强制更新会在CheckForUpdateAsync中自动显示更新页面
                        }
                        else
                        {
                            logger.LogInformation("检测到可选更新，显示确认对话框");
                            bool shouldUpdate = await DisplayAlert(
                                "发现新版本",
                                "是否立即更新？",
                                "更新",
                                "稍后");

                            if (shouldUpdate)
                            {
                                await ShowUpdatePageAsync(updateService, logger);
                            }
                        }
                    });
                }
                else
                {
                    logger.LogInformation("当前已是最新版本");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Shell更新检查失败: {ex.Message}");
            }
        }

        private async Task ShowUpdatePageAsync(IClientUpdateService updateService, IApplicationLogger logger)
        {
            try
            {
#if ANDROID
                if (updateService is Platforms.Android.AndroidUpdateService androidUpdateService)
                {
                    await androidUpdateService.ShowUpdatePageAsync();
                }
                else
#endif
                {
                    logger.LogWarning("当前平台不支持显示更新页面");
                }
            }
            catch (Exception ex)
            {
                logger?.LogError(ex, "显示更新页面失败: {Message}", ex.Message);
            }
        }
    }
}
