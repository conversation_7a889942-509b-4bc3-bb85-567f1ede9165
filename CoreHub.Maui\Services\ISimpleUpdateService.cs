using CoreHub.Shared.Models.AppUpdate;

namespace CoreHub.Maui.Services
{
    /// <summary>
    /// 简化的更新服务接口，参考MauiScanManager设计
    /// </summary>
    public interface ISimpleUpdateService
    {
        /// <summary>
        /// 检查更新
        /// </summary>
        Task<bool> CheckForUpdate();
        
        /// <summary>
        /// 下载并安装更新
        /// </summary>
        Task<bool> DownloadAndInstallUpdate();
        
        /// <summary>
        /// 更新进度事件
        /// </summary>
        event EventHandler<UpdateProgressEventArgs> UpdateProgress;
        
        /// <summary>
        /// 是否强制更新
        /// </summary>
        bool IsForceUpdate { get; }
        
        /// <summary>
        /// 是否正在更新
        /// </summary>
        bool IsUpdating { get; }
        
        /// <summary>
        /// 是否已检查过更新
        /// </summary>
        bool HasChecked { get; }
    }

    /// <summary>
    /// 更新进度事件参数
    /// </summary>
    public class UpdateProgressEventArgs : EventArgs
    {
        public double Progress { get; set; }
        public string Status { get; set; } = string.Empty;
    }
}
