<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="CoreHub.Maui.Pages.UpdateTestPage"
             Title="更新测试">
    <ScrollView>
        <StackLayout Padding="20" Spacing="20">
            
            <!-- 当前版本信息 -->
            <Frame BackgroundColor="White" 
                   CornerRadius="10" 
                   HasShadow="True"
                   Padding="20">
                <StackLayout>
                    <Label Text="当前版本信息" 
                           FontSize="18" 
                           FontAttributes="Bold"
                           TextColor="#333333"/>
                    <Label x:Name="CurrentVersionLabel" 
                           Text="版本: 加载中..." 
                           TextColor="#666666"/>
                    <Label x:Name="UpdateStatusLabel" 
                           Text="更新状态: 未检查" 
                           TextColor="#666666"/>
                </StackLayout>
            </Frame>

            <!-- 操作按钮 -->
            <StackLayout Spacing="10">
                <Button x:Name="CheckUpdateButton"
                        Text="检查更新"
                        BackgroundColor="#007ACC"
                        TextColor="White"
                        FontSize="16"
                        HeightRequest="50"
                        CornerRadius="25"
                        Clicked="OnCheckUpdateClicked"/>
                
                <Button x:Name="ForceUpdateButton"
                        Text="强制更新测试"
                        BackgroundColor="#FF6B6B"
                        TextColor="White"
                        FontSize="16"
                        HeightRequest="50"
                        CornerRadius="25"
                        Clicked="OnForceUpdateClicked"/>
                
                <Button x:Name="ShowUpdatePageButton"
                        Text="显示更新页面"
                        BackgroundColor="#4ECDC4"
                        TextColor="White"
                        FontSize="16"
                        HeightRequest="50"
                        CornerRadius="25"
                        Clicked="OnShowUpdatePageClicked"/>
            </StackLayout>

            <!-- 日志显示 -->
            <Frame BackgroundColor="White" 
                   CornerRadius="10" 
                   HasShadow="True"
                   Padding="20">
                <StackLayout>
                    <Label Text="操作日志" 
                           FontSize="18" 
                           FontAttributes="Bold"
                           TextColor="#333333"/>
                    <ScrollView HeightRequest="200">
                        <Label x:Name="LogLabel" 
                               Text="等待操作..." 
                               TextColor="#666666"
                               FontSize="12"/>
                    </ScrollView>
                    <Button Text="清除日志"
                            BackgroundColor="#95A5A6"
                            TextColor="White"
                            FontSize="14"
                            HeightRequest="40"
                            CornerRadius="20"
                            Clicked="OnClearLogClicked"/>
                </StackLayout>
            </Frame>

        </StackLayout>
    </ScrollView>
</ContentPage>
